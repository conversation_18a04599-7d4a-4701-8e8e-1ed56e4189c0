<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad_eos" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#grad_eos)" filter="url(#shadow)"/>
  
  <!-- 币种符号 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" 
        text-anchor="middle" fill="#FFFFFF" filter="url(#shadow)">
    EOS
  </text>
  
  <!-- 装饰性边框 -->
  <circle cx="32" cy="32" r="30" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.3"/>
</svg>